#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data validation script

This script helps validate that your data files are in the correct format
before running the RF estimation analysis.
"""

import os
import numpy as np
import scipy.io as sio
import matplotlib.pyplot as plt
from config import IMAGE_FILE, KILOSORT_FILE, EXPECTED_IMAGE_SIZE

try:
    import h5py
    HDF5_AVAILABLE = True
except ImportError:
    HDF5_AVAILABLE = False
    print("Warning: h5py not available. Install with: pip install h5py")

def load_mat_file(file_path):
    """
    Load .mat file, handling both v7.3 (HDF5) and older formats

    Parameters:
    file_path: path to .mat file

    Returns:
    data: loaded data dictionary
    file_version: 'v7.3' or 'older'
    """
    try:
        # First try scipy.io (for older formats)
        data = sio.loadmat(file_path)
        return data, 'older'
    except NotImplementedError:
        # If that fails, try h5py (for v7.3 format)
        if not HDF5_AVAILABLE:
            raise ImportError("h5py is required for MATLAB v7.3 files. Install with: pip install h5py")

        with h5py.File(file_path, 'r') as f:
            data = {}
            for key in f.keys():
                data[key] = f[key]
            return data, 'v7.3'

def extract_cell_array_h5(h5_dataset):
    """
    Extract cell array data from HDF5 dataset

    Parameters:
    h5_dataset: HDF5 dataset containing cell array

    Returns:
    cell_data: list of arrays from the cell array
    """
    if len(h5_dataset.shape) != 2:
        raise ValueError(f"Expected 2D cell array, got shape {h5_dataset.shape}")

    cell_data = []
    num_cells = h5_dataset.shape[0]

    with h5py.File(h5_dataset.file.filename, 'r') as f:
        for i in range(num_cells):
            # Get reference to the cell content
            cell_ref = h5_dataset[i, 0]
            if isinstance(cell_ref, h5py.Reference):
                # Dereference to get the actual data
                cell_content = f[cell_ref][()]
                cell_data.append(cell_content)
            else:
                cell_data.append(cell_ref)

    return cell_data

def validate_image_file(file_path):
    """
    Validate the image data file format
    
    Parameters:
    file_path: path to the image .mat file
    
    Returns:
    is_valid: boolean indicating if the file is valid
    info: dictionary with file information
    """
    print(f"Validating image file: {file_path}")
    
    info = {}
    is_valid = True
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"❌ File does not exist: {file_path}")
            return False, info
        
        # Load the .mat file
        mat_data, file_version = load_mat_file(file_path)
        info['file_size_mb'] = os.path.getsize(file_path) / (1024 * 1024)
        info['file_version'] = file_version
        info['mat_variables'] = list(mat_data.keys())

        print(f"✅ File exists ({info['file_size_mb']:.1f} MB)")
        print(f"✅ File version: MATLAB {file_version}")
        print(f"✅ Variables in file: {[k for k in info['mat_variables'] if not k.startswith('__')]}")

        # Check for 'imgset' variable
        if 'imgset' not in mat_data:
            print("❌ 'imgset' variable not found in file")
            print(f"   Available variables: {[k for k in info['mat_variables'] if not k.startswith('__')]}")
            return False, info

        imgset = mat_data['imgset']

        # Handle different file formats
        if file_version == 'v7.3':
            # For HDF5 format, imgset is an HDF5 dataset
            info['imgset_shape'] = imgset.shape
            info['imgset_dtype'] = imgset.dtype
            print(f"✅ 'imgset' variable found (HDF5 format) with shape: {info['imgset_shape']}")
        else:
            # For older format, imgset is a numpy array
            info['imgset_shape'] = imgset.shape
            info['imgset_dtype'] = imgset.dtype
            print(f"✅ 'imgset' variable found (older format) with shape: {info['imgset_shape']}")
        
        print(f"✅ 'imgset' variable found with shape: {info['imgset_shape']}")
        
        # Check imgset dimensions
        if len(imgset.shape) != 2 or imgset.shape[1] != 1:
            print(f"❌ 'imgset' should be Nx1 cell array, got shape: {imgset.shape}")
            is_valid = False
        else:
            print(f"✅ 'imgset' has correct cell array structure")
        
        # Check first few images
        num_images = imgset.shape[0]
        info['num_images'] = num_images
        print(f"✅ Number of images: {num_images}")
        
        # Sample a few images to check format
        sample_indices = [0, num_images//2, num_images-1]
        image_shapes = []
        image_dtypes = []

        for i in sample_indices:
            try:
                if file_version == 'v7.3':
                    # For HDF5 format, need to handle references
                    with h5py.File(file_path, 'r') as f:
                        cell_ref = imgset[i, 0]
                        if isinstance(cell_ref, h5py.Reference):
                            img = f[cell_ref][()]
                        else:
                            img = cell_ref
                else:
                    # For older format
                    img = imgset[i, 0]

                image_shapes.append(img.shape)
                image_dtypes.append(img.dtype)
            except Exception as e:
                print(f"❌ Error accessing image {i}: {e}")
                is_valid = False
                continue
        
        info['sample_image_shapes'] = image_shapes
        info['sample_image_dtypes'] = image_dtypes
        
        # Check image dimensions consistency
        if len(set(image_shapes)) == 1:
            img_shape = image_shapes[0]
            info['image_shape'] = img_shape
            print(f"✅ All sampled images have consistent shape: {img_shape}")
            
            # Check if shape matches expected
            if img_shape == EXPECTED_IMAGE_SIZE:
                print(f"✅ Image shape matches expected: {EXPECTED_IMAGE_SIZE}")
            else:
                print(f"⚠️  Image shape {img_shape} differs from expected {EXPECTED_IMAGE_SIZE}")
                print("   You may need to update EXPECTED_IMAGE_SIZE in config.py")
        else:
            print(f"❌ Inconsistent image shapes found: {set(image_shapes)}")
            is_valid = False
        
        # Check data types
        if len(set(image_dtypes)) == 1:
            print(f"✅ All sampled images have consistent dtype: {image_dtypes[0]}")
        else:
            print(f"⚠️  Mixed image dtypes found: {set(image_dtypes)}")
        
        # Check image value ranges
        if file_version == 'v7.3':
            with h5py.File(file_path, 'r') as f:
                cell_ref = imgset[0, 0]
                if isinstance(cell_ref, h5py.Reference):
                    sample_img = f[cell_ref][()]
                else:
                    sample_img = cell_ref
        else:
            sample_img = imgset[0, 0]

        info['image_min'] = float(np.min(sample_img))
        info['image_max'] = float(np.max(sample_img))
        info['image_mean'] = float(np.mean(sample_img))
        info['image_std'] = float(np.std(sample_img))
        
        print(f"✅ Sample image statistics:")
        print(f"   Min: {info['image_min']:.3f}, Max: {info['image_max']:.3f}")
        print(f"   Mean: {info['image_mean']:.3f}, Std: {info['image_std']:.3f}")
        
        if info['image_min'] >= 0 and info['image_max'] <= 1:
            print("✅ Image values appear to be normalized [0,1]")
        elif info['image_min'] >= 0 and info['image_max'] <= 255:
            print("⚠️  Image values appear to be in [0,255] range - may need normalization")
        else:
            print("⚠️  Unusual image value range detected")
        
    except Exception as e:
        print(f"❌ Error validating image file: {e}")
        is_valid = False
    
    return is_valid, info

def validate_kilosort_file(file_path):
    """
    Validate the kilosort results file format
    
    Parameters:
    file_path: path to the kilosort .mat file
    
    Returns:
    is_valid: boolean indicating if the file is valid
    info: dictionary with file information
    """
    print(f"\nValidating kilosort file: {file_path}")
    
    info = {}
    is_valid = True
    
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"❌ File does not exist: {file_path}")
            return False, info
        
        # Load the .mat file
        mat_data = sio.loadmat(file_path)
        info['file_size_mb'] = os.path.getsize(file_path) / (1024 * 1024)
        info['mat_variables'] = list(mat_data.keys())
        
        print(f"✅ File exists ({info['file_size_mb']:.1f} MB)")
        print(f"✅ Variables in file: {[k for k in info['mat_variables'] if not k.startswith('__')]}")
        
        # Check for required variables
        required_vars = ['spike0_kilosort3', 'ex']
        missing_vars = []
        
        for var in required_vars:
            if var not in mat_data:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing required variables: {missing_vars}")
            is_valid = False
        else:
            print("✅ All required variables found")
        
        # Validate spike0_kilosort3 structure
        if 'spike0_kilosort3' in mat_data:
            spike_data = mat_data['spike0_kilosort3']
            
            # Check if it's a structure
            if hasattr(spike_data, 'dtype') and spike_data.dtype.names:
                spike_fields = spike_data.dtype.names
                info['spike_fields'] = spike_fields
                print(f"✅ spike0_kilosort3 structure fields: {spike_fields}")
                
                required_spike_fields = ['time', 'template']
                missing_spike_fields = [f for f in required_spike_fields if f not in spike_fields]
                
                if missing_spike_fields:
                    print(f"❌ Missing spike fields: {missing_spike_fields}")
                    is_valid = False
                else:
                    print("✅ All required spike fields found")
                    
                    # Get spike data statistics
                    spike_times = spike_data['time'][0, 0].flatten()
                    spike_templates = spike_data['template'][0, 0].flatten()
                    
                    info['num_spikes'] = len(spike_times)
                    info['unique_templates'] = np.unique(spike_templates)
                    info['num_templates'] = len(info['unique_templates'])
                    info['spike_time_range'] = (float(np.min(spike_times)), float(np.max(spike_times)))
                    
                    print(f"✅ Spike statistics:")
                    print(f"   Total spikes: {info['num_spikes']}")
                    print(f"   Unique templates: {info['num_templates']}")
                    print(f"   Template IDs: {info['unique_templates']}")
                    print(f"   Time range: {info['spike_time_range'][0]:.3f} - {info['spike_time_range'][1]:.3f}")
            else:
                print("❌ spike0_kilosort3 is not a proper structure")
                is_valid = False
        
        # Validate ex structure
        if 'ex' in mat_data:
            ex_data = mat_data['ex']
            
            try:
                cond_index = ex_data['CondTest'][0, 0]['CondIndex'][0, 0].flatten()
                info['num_conditions'] = len(cond_index)
                info['unique_conditions'] = np.unique(cond_index)
                info['condition_range'] = (int(np.min(cond_index)), int(np.max(cond_index)))
                
                print(f"✅ Condition statistics:")
                print(f"   Total conditions: {info['num_conditions']}")
                print(f"   Unique conditions: {len(info['unique_conditions'])}")
                print(f"   Condition range: {info['condition_range'][0]} - {info['condition_range'][1]}")
                
            except Exception as e:
                print(f"❌ Error accessing ex.CondTest.CondIndex: {e}")
                is_valid = False
        
    except Exception as e:
        print(f"❌ Error validating kilosort file: {e}")
        is_valid = False
    
    return is_valid, info

def plot_sample_data(image_info, kilosort_info):
    """
    Plot sample data for visual inspection
    """
    print("\nGenerating sample data plots...")

    try:
        # Load and plot sample images
        mat_data, file_version = load_mat_file(IMAGE_FILE)
        imgset = mat_data['imgset']

        _, axes = plt.subplots(2, 3, figsize=(12, 8))

        # Plot sample images
        for i in range(6):
            row = i // 3
            col = i % 3

            img_idx = i * (imgset.shape[0] // 6)

            # Handle different file formats
            if file_version == 'v7.3':
                with h5py.File(IMAGE_FILE, 'r') as f:
                    cell_ref = imgset[img_idx, 0]
                    if isinstance(cell_ref, h5py.Reference):
                        img = f[cell_ref][()]
                    else:
                        img = cell_ref
            else:
                img = imgset[img_idx, 0]

            axes[row, col].imshow(img, cmap='gray')
            axes[row, col].set_title(f'Image {img_idx}')
            axes[row, col].axis('off')
        
        plt.suptitle('Sample Images from Dataset')
        plt.tight_layout()
        plt.savefig('Results/sample_images.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Plot spike data if available
        if os.path.exists(KILOSORT_FILE):
            kilosort_data = sio.loadmat(KILOSORT_FILE)
            spike_times = kilosort_data['spike0_kilosort3']['time'][0, 0].flatten()
            spike_templates = kilosort_data['spike0_kilosort3']['template'][0, 0].flatten()
            
            plt.figure(figsize=(12, 6))
            
            plt.subplot(1, 2, 1)
            plt.hist(spike_times, bins=50, alpha=0.7)
            plt.xlabel('Spike Time')
            plt.ylabel('Count')
            plt.title('Spike Time Distribution')
            plt.grid(True, alpha=0.3)
            
            plt.subplot(1, 2, 2)
            unique_templates, counts = np.unique(spike_templates, return_counts=True)
            plt.bar(unique_templates, counts, alpha=0.7)
            plt.xlabel('Template ID')
            plt.ylabel('Spike Count')
            plt.title('Spikes per Template')
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('Results/spike_statistics.png', dpi=300, bbox_inches='tight')
            plt.show()
        
        print("✅ Sample plots saved to Results/ directory")
        
    except Exception as e:
        print(f"❌ Error generating sample plots: {e}")

def main():
    """
    Main validation function
    """
    print("=== DATA VALIDATION SCRIPT ===")
    print("This script will validate your data files before running RF estimation.\n")
    
    # Create results directory
    os.makedirs('Results', exist_ok=True)
    
    # Validate image file
    image_valid, image_info = validate_image_file(IMAGE_FILE)
    
    # Validate kilosort file
    kilosort_valid, kilosort_info = validate_kilosort_file(KILOSORT_FILE)
    
    # Summary
    print("\n" + "="*50)
    print("VALIDATION SUMMARY")
    print("="*50)
    
    if image_valid:
        print("✅ Image file validation: PASSED")
    else:
        print("❌ Image file validation: FAILED")
    
    if kilosort_valid:
        print("✅ Kilosort file validation: PASSED")
    else:
        print("❌ Kilosort file validation: FAILED")
    
    if image_valid and kilosort_valid:
        print("\n🎉 All validations passed! You can proceed with RF estimation.")
        
        # Generate sample plots
        plot_sample_data(image_info, kilosort_info)
        
        print("\nNext steps:")
        print("1. Review the sample plots in Results/ directory")
        print("2. Adjust config.py parameters if needed")
        print("3. Run: python custom_data_sysiden.py")
        
    else:
        print("\n⚠️  Some validations failed. Please fix the issues before proceeding.")
        print("\nCommon fixes:")
        print("- Check file paths in config.py")
        print("- Verify data file formats match requirements")
        print("- Ensure all required variables are present in .mat files")
    
    # Save validation report
    validation_report = {
        'image_file': IMAGE_FILE,
        'kilosort_file': KILOSORT_FILE,
        'image_valid': image_valid,
        'kilosort_valid': kilosort_valid,
        'image_info': image_info,
        'kilosort_info': kilosort_info
    }
    
    import json
    with open('Results/validation_report.json', 'w') as f:
        json.dump(validation_report, f, indent=2, default=str)
    
    print(f"\nValidation report saved to: Results/validation_report.json")

if __name__ == "__main__":
    main()
