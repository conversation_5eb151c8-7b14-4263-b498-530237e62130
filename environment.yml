name: tensorflow
channels:
  - defaults
dependencies:
  - _tflow_select=2.2.0=eigen
  - abseil-cpp=20211102.0=hc377ac9_0
  - absl-py=1.4.0=py311hca03da5_0
  - aiohttp=3.8.5=py311h80987f9_0
  - aiosignal=1.2.0=pyhd3eb1b0_0
  - alabaster=0.7.12=pyhd3eb1b0_0
  - applaunchservices=0.3.0=py311hca03da5_0
  - appnope=0.1.2=py311hca03da5_1001
  - arrow=1.2.3=py311hca03da5_1
  - astroid=2.14.2=py311hca03da5_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - astunparse=1.6.3=py_0
  - async-timeout=4.0.2=py311hca03da5_0
  - atomicwrites=1.4.0=py_0
  - attrs=23.1.0=py311hca03da5_0
  - autopep8=1.6.0=pyhd3eb1b0_1
  - babel=2.11.0=py311hca03da5_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - beautifulsoup4=4.12.2=py311hca03da5_0
  - binaryornot=0.4.4=pyhd3eb1b0_1
  - black=23.3.0=py311hca03da5_0
  - blas=1.0=openblas
  - bleach=4.1.0=pyhd3eb1b0_0
  - blinker=1.4=py311hca03da5_0
  - brotli=1.0.9=h1a28f6b_7
  - brotli-bin=1.0.9=h1a28f6b_7
  - brotlipy=0.7.0=py311h80987f9_1002
  - bzip2=1.0.8=h620ffc9_4
  - c-ares=1.19.1=h80987f9_0
  - ca-certificates=2023.08.22=hca03da5_0
  - cachetools=4.2.2=pyhd3eb1b0_0
  - certifi=2023.7.22=py311hca03da5_0
  - cffi=1.15.1=py311h80987f9_3
  - chardet=4.0.0=py311hca03da5_1003
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - click=8.0.4=py311hca03da5_0
  - cloudpickle=2.2.1=py311hca03da5_0
  - colorama=0.4.6=py311hca03da5_0
  - comm=0.1.2=py311hca03da5_0
  - contourpy=1.0.5=py311h48ca7d4_0
  - cookiecutter=1.7.3=pyhd3eb1b0_0
  - cryptography=41.0.3=py311h3c57c4d_0
  - cycler=0.11.0=pyhd3eb1b0_0
  - cyrus-sasl=2.1.28=h458e800_1
  - debugpy=1.6.7=py311h313beb8_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - diff-match-patch=20200713=pyhd3eb1b0_0
  - dill=0.3.7=py311hca03da5_0
  - docstring-to-markdown=0.11=py311hca03da5_0
  - docutils=0.18.1=py311hca03da5_3
  - entrypoints=0.4=py311hca03da5_0
  - executing=0.8.3=pyhd3eb1b0_0
  - flake8=6.0.0=py311hca03da5_0
  - flatbuffers=2.0.0=hc377ac9_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.12.1=h1192e45_0
  - frozenlist=1.3.3=py311h80987f9_0
  - gast=0.4.0=pyhd3eb1b0_0
  - gettext=0.21.0=h13f89a0_1
  - giflib=5.2.1=h80987f9_3
  - glib=2.69.1=h514c7bf_2
  - google-auth=2.22.0=py311hca03da5_0
  - google-auth-oauthlib=0.5.2=py311hca03da5_0
  - google-pasta=0.2.0=pyhd3eb1b0_0
  - grpc-cpp=1.48.2=h877324c_0
  - grpcio=1.48.2=py311h877324c_0
  - gst-plugins-base=1.14.1=h313beb8_1
  - gstreamer=1.14.1=h80987f9_1
  - h5py=3.9.0=py311hba6ad2f_0
  - hdf5=1.12.1=h160e8cb_2
  - icu=68.1=hc377ac9_0
  - idna=3.4=py311hca03da5_0
  - imagesize=1.4.1=py311hca03da5_0
  - importlib-metadata=6.0.0=py311hca03da5_0
  - importlib_metadata=6.0.0=hd3eb1b0_0
  - inflection=0.5.1=py311hca03da5_0
  - intervaltree=3.1.0=pyhd3eb1b0_0
  - ipykernel=6.25.0=py311hb6e6a13_0
  - ipython=8.15.0=py311hca03da5_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - isort=5.9.3=pyhd3eb1b0_0
  - jaraco.classes=3.2.1=pyhd3eb1b0_0
  - jedi=0.18.1=py311hca03da5_1
  - jellyfish=1.0.1=py311h15d1925_0
  - jinja2=3.1.2=py311hca03da5_0
  - jinja2-time=0.2.0=pyhd3eb1b0_3
  - joblib=1.2.0=py311hca03da5_0
  - jpeg=9e=h80987f9_1
  - jsonschema=4.17.3=py311hca03da5_0
  - jupyter_client=8.1.0=py311hca03da5_0
  - jupyter_core=5.3.0=py311hca03da5_0
  - jupyterlab_pygments=0.1.2=py_0
  - keras=2.12.0=py311hca03da5_0
  - keras-preprocessing=1.1.2=pyhd3eb1b0_0
  - keyring=23.13.1=py311hca03da5_0
  - kiwisolver=1.4.4=py311h313beb8_0
  - krb5=1.20.1=h8380606_1
  - lazy-object-proxy=1.6.0=py311h80987f9_0
  - lcms2=2.12=hba8e193_0
  - lerc=3.0=hc377ac9_0
  - libbrotlicommon=1.0.9=h1a28f6b_7
  - libbrotlidec=1.0.9=h1a28f6b_7
  - libbrotlienc=1.0.9=h1a28f6b_7
  - libclang=14.0.6=default_h1b80db6_1
  - libclang13=14.0.6=default_h24352ff_1
  - libcurl=8.2.1=h0f1d93c_0
  - libcxx=14.0.6=h848a8c0_0
  - libdeflate=1.17=h80987f9_1
  - libedit=3.1.20221030=h80987f9_0
  - libev=4.33=h1a28f6b_1
  - libffi=3.4.4=hca03da5_0
  - libgfortran=5.0.0=11_3_0_hca03da5_28
  - libgfortran5=11.3.0=h009349e_28
  - libiconv=1.16=h1a28f6b_2
  - libllvm14=14.0.6=h7ec7a93_3
  - libnghttp2=1.52.0=h10c0552_1
  - libopenblas=0.3.21=h269037a_0
  - libpng=1.6.39=h80987f9_0
  - libpq=12.15=h449679c_1
  - libprotobuf=3.20.3=h514c7bf_0
  - libsodium=1.0.18=h1a28f6b_0
  - libspatialindex=1.9.3=hc377ac9_0
  - libssh2=1.10.0=h449679c_2
  - libtiff=4.5.1=h313beb8_0
  - libwebp=1.3.2=ha3663a8_0
  - libwebp-base=1.3.2=h80987f9_0
  - libxml2=2.10.4=h372ba2a_0
  - libxslt=1.1.37=habca612_0
  - llvm-openmp=14.0.6=hc6e5704_0
  - lxml=4.9.3=py311h50ffb84_0
  - lz4-c=1.9.4=h313beb8_0
  - markdown=3.4.1=py311hca03da5_0
  - markupsafe=2.1.1=py311h80987f9_0
  - matplotlib=3.7.2=py311hca03da5_0
  - matplotlib-base=3.7.2=py311h7aedaa7_0
  - matplotlib-inline=0.1.6=py311hca03da5_0
  - mccabe=0.7.0=pyhd3eb1b0_0
  - mistune=0.8.4=py311h80987f9_1000
  - more-itertools=8.12.0=pyhd3eb1b0_0
  - multidict=6.0.2=py311h80987f9_0
  - munkres=1.1.4=py_0
  - mypy_extensions=1.0.0=py311hca03da5_0
  - mysql=5.7.24=he1ceea5_2
  - nbclient=0.5.13=py311hca03da5_0
  - nbconvert=6.5.4=py311hca03da5_0
  - nbformat=5.9.2=py311hca03da5_0
  - ncurses=6.4=h313beb8_0
  - nest-asyncio=1.5.6=py311hca03da5_0
  - numpy=1.23.5=py311hb2c0538_0
  - numpy-base=1.23.5=py311h9eb1c70_0
  - numpydoc=1.5.0=py311hca03da5_0
  - oauthlib=3.2.2=py311hca03da5_0
  - openssl=1.1.1w=h1a28f6b_0
  - opt_einsum=3.3.0=pyhd3eb1b0_1
  - packaging=23.1=py311hca03da5_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pathspec=0.10.3=py311hca03da5_0
  - pcre=8.45=hc377ac9_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.4.0=py311h313beb8_1
  - pip=23.2.1=py311hca03da5_0
  - platformdirs=3.10.0=py311hca03da5_0
  - pluggy=1.0.0=py311hca03da5_1
  - ply=3.11=py311hca03da5_0
  - poyo=0.5.0=pyhd3eb1b0_0
  - prompt-toolkit=3.0.36=py311hca03da5_0
  - protobuf=3.20.3=py311h313beb8_0
  - psutil=5.9.0=py311h80987f9_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pyasn1=0.4.8=pyhd3eb1b0_0
  - pyasn1-modules=0.2.8=py_0
  - pycodestyle=2.10.0=py311hca03da5_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pydocstyle=6.3.0=py311hca03da5_0
  - pyflakes=3.0.1=py311hca03da5_0
  - pygments=2.15.1=py311hca03da5_1
  - pyjwt=2.4.0=py311hca03da5_0
  - pylint=2.16.2=py311hca03da5_0
  - pylint-venv=2.3.0=py311hca03da5_0
  - pyls-spyder=0.4.0=pyhd3eb1b0_0
  - pyobjc-core=9.0=py311h3eb5a62_1
  - pyobjc-framework-cocoa=9.0=py311hb094c41_0
  - pyobjc-framework-coreservices=9.0=py311hdd8dd1f_0
  - pyobjc-framework-fsevents=9.0=py311hca03da5_0
  - pyopenssl=23.2.0=py311hca03da5_0
  - pyparsing=3.0.9=py311hca03da5_0
  - pyqt=5.15.7=py311h313beb8_0
  - pyqt5-sip=12.11.0=py311h313beb8_0
  - pyqtwebengine=5.15.7=py311h313beb8_0
  - pyrsistent=0.18.0=py311h80987f9_0
  - pysocks=1.7.1=py311hca03da5_0
  - python=3.11.5=hc0d8a6c_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py311hca03da5_0
  - python-flatbuffers=2.0=pyhd3eb1b0_0
  - python-lsp-black=1.2.1=py311hca03da5_0
  - python-lsp-jsonrpc=1.0.0=pyhd3eb1b0_0
  - python-lsp-server=1.7.2=py311hca03da5_0
  - python-slugify=5.0.2=pyhd3eb1b0_0
  - python.app=3=py311h80987f9_0
  - pytoolconfig=1.2.5=py311hca03da5_1
  - pytz=2023.3.post1=py311hca03da5_0
  - pyyaml=6.0=py311h80987f9_1
  - pyzmq=25.1.0=py311h313beb8_0
  - qdarkstyle=3.0.2=pyhd3eb1b0_0
  - qstylizer=0.2.2=py311hca03da5_0
  - qt-main=5.15.2=h8c8f68c_9
  - qt-webengine=5.15.9=h2903aaf_7
  - qtawesome=1.2.2=py311hca03da5_0
  - qtconsole=5.4.2=py311hca03da5_0
  - qtpy=2.2.0=py311hca03da5_0
  - qtwebkit=5.212=h19f419d_5
  - re2=2022.04.01=hc377ac9_0
  - readline=8.2=h1a28f6b_0
  - requests=2.31.0=py311hca03da5_0
  - requests-oauthlib=1.3.0=py_0
  - rope=1.7.0=py311hca03da5_0
  - rsa=4.7.2=pyhd3eb1b0_1
  - rtree=1.0.1=py311hca03da5_0
  - scikit-learn=1.3.0=py311h7aedaa7_0
  - scipy=1.11.1=py311hc76d9b0_0
  - setuptools=68.0.0=py311hca03da5_0
  - sip=6.6.2=py311h313beb8_0
  - six=1.16.0=pyhd3eb1b0_1
  - snappy=1.1.9=hc377ac9_0
  - snowballstemmer=2.2.0=pyhd3eb1b0_0
  - sortedcontainers=2.4.0=pyhd3eb1b0_0
  - soupsieve=2.5=py311hca03da5_0
  - sphinx=5.0.2=py311hca03da5_0
  - sphinxcontrib-applehelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-devhelp=1.0.2=pyhd3eb1b0_0
  - sphinxcontrib-htmlhelp=2.0.0=pyhd3eb1b0_0
  - sphinxcontrib-jsmath=1.0.1=pyhd3eb1b0_0
  - sphinxcontrib-qthelp=1.0.3=pyhd3eb1b0_0
  - sphinxcontrib-serializinghtml=1.1.5=pyhd3eb1b0_0
  - spyder=5.4.3=py311hca03da5_1
  - spyder-kernels=2.4.4=py311hca03da5_0
  - sqlite=3.41.2=h80987f9_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - tensorboard=2.12.1=py311hca03da5_0
  - tensorboard-data-server=0.7.0=py311ha6e5c4f_0
  - tensorboard-plugin-wit=1.8.1=py311hca03da5_0
  - tensorflow=2.12.0=eigen_py311h689d69b_0
  - tensorflow-base=2.12.0=eigen_py311h0a52ebb_0
  - tensorflow-estimator=2.12.0=py311hca03da5_0
  - termcolor=2.1.0=py311hca03da5_0
  - text-unidecode=1.3=pyhd3eb1b0_0
  - textdistance=4.2.1=pyhd3eb1b0_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - three-merge=0.1.1=pyhd3eb1b0_0
  - tinycss2=1.2.1=py311hca03da5_0
  - tk=8.6.12=hb8d0fd4_0
  - toml=0.10.2=pyhd3eb1b0_0
  - tomlkit=0.11.1=py311hca03da5_0
  - tornado=6.3.2=py311h80987f9_0
  - traitlets=5.7.1=py311hca03da5_0
  - typing_extensions=4.7.1=py311hca03da5_0
  - tzdata=2023c=h04d1e81_0
  - ujson=5.4.0=py311h313beb8_0
  - unidecode=1.2.0=pyhd3eb1b0_0
  - urllib3=1.26.16=py311hca03da5_0
  - watchdog=2.1.6=py311h80987f9_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py311hca03da5_1
  - werkzeug=2.2.3=py311hca03da5_0
  - whatthepatch=1.0.2=py311hca03da5_0
  - wheel=0.35.1=pyhd3eb1b0_0
  - wrapt=1.14.1=py311h80987f9_0
  - wurlitzer=3.0.2=py311hca03da5_0
  - xz=5.4.2=h80987f9_0
  - yaml=0.2.5=h1a28f6b_0
  - yapf=0.31.0=pyhd3eb1b0_0
  - yarl=1.8.1=py311h80987f9_0
  - zeromq=4.3.4=hc377ac9_0
  - zipp=3.11.0=py311hca03da5_0
  - zlib=1.2.13=h5a0b063_0
  - zstd=1.5.5=hd90d995_0
prefix: /Users/<USER>/anaconda3/envs/tensorflow
