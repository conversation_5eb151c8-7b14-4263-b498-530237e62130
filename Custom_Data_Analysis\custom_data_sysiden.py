#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on 2025-01-29

Custom script for RF estimation using user's own data format:
- Natural images stored in .mat file with 'imgset' variable (24000x1 cell array)
- Each cell contains 64x64 double array (grayscale images)
- Kilosort3 results with spike times, templates, and stimulus indices

This script adapts the original CNNwithPReLU_RFestimateV1 method for custom data format.
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK']='True'  

import numpy as np
import matplotlib.pyplot as plt
import scipy.io as sio
from tensorflow import keras
from tensorflow.keras.callbacks import ModelCheckpoint
import sys

# Add the original support files to path
sys.path.insert(0, '../Simulated Neuron/SupportFiles/')
from k_functions import buildCalcAndScale, conv_output_length, plotGaussMap, plotReconstruction
from k_model import model_pass1, model_pass2

def load_custom_images(mat_file_path):
    """
    Load natural images from custom .mat file format
    
    Parameters:
    mat_file_path: path to .mat file containing 'imgset' variable
    
    Returns:
    stim: numpy array of shape (height, width, num_images)
    """
    print(f"Loading images from {mat_file_path}")
    
    # Load the .mat file
    mat_data = sio.loadmat(mat_file_path)
    imgset = mat_data['imgset']
    
    print(f"Loaded imgset with shape: {imgset.shape}")
    
    # Extract images from cell array
    num_images = imgset.shape[0]
    
    # Get the first image to determine dimensions
    first_img = imgset[0, 0]
    img_height, img_width = first_img.shape
    
    print(f"Image dimensions: {img_height}x{img_width}, Number of images: {num_images}")
    
    # Initialize the stimulus array
    stim = np.zeros((img_height, img_width, num_images), dtype=np.float32)
    
    # Extract all images
    for i in range(num_images):
        img = imgset[i, 0]
        stim[:, :, i] = img.astype(np.float32)
    
    print(f"Final stimulus array shape: {stim.shape}")
    return stim

def load_kilosort_data(mat_file_path, neuron_id=0):
    """
    Load kilosort3 results and extract spike data for a specific neuron
    
    Parameters:
    mat_file_path: path to .mat file containing kilosort results
    neuron_id: which neuron template to analyze (default: 0)
    
    Returns:
    spike_times: array of spike times for the selected neuron
    stimulus_indices: array of stimulus indices corresponding to each spike
    """
    print(f"Loading kilosort data from {mat_file_path}")
    
    # Load the .mat file
    mat_data = sio.loadmat(mat_file_path)
    
    # Extract spike data
    spike_times = mat_data['spike0_kilosort3']['time'][0, 0].flatten()
    spike_templates = mat_data['spike0_kilosort3']['template'][0, 0].flatten()
    stimulus_indices = mat_data['ex']['CondTest'][0, 0]['CondIndex'][0, 0].flatten()
    
    print(f"Total spikes: {len(spike_times)}")
    print(f"Unique templates: {np.unique(spike_templates)}")
    print(f"Stimulus indices range: {stimulus_indices.min()} to {stimulus_indices.max()}")
    
    # Filter spikes for the selected neuron
    neuron_mask = spike_templates == neuron_id
    neuron_spike_times = spike_times[neuron_mask]
    
    print(f"Spikes for neuron {neuron_id}: {len(neuron_spike_times)}")
    
    return neuron_spike_times, stimulus_indices

def compute_neural_responses(spike_times, stimulus_indices, num_stimuli, 
                           stimulus_duration=1.0, response_window=0.5):
    """
    Compute neural responses for each stimulus presentation
    
    Parameters:
    spike_times: array of spike times for the neuron
    stimulus_indices: array of stimulus indices
    num_stimuli: total number of stimuli
    stimulus_duration: duration of each stimulus presentation (seconds)
    response_window: time window to count spikes after stimulus onset (seconds)
    
    Returns:
    responses: array of spike counts for each stimulus
    """
    print("Computing neural responses...")
    
    responses = np.zeros(num_stimuli)
    
    # For each stimulus, count spikes in the response window
    for stim_idx in range(num_stimuli):
        # Find stimulus presentations for this stimulus
        stim_presentations = np.where(stimulus_indices == stim_idx)[0]
        
        if len(stim_presentations) > 0:
            # For simplicity, use the first presentation
            # In practice, you might want to average across multiple presentations
            presentation_time = stim_presentations[0] * stimulus_duration
            
            # Count spikes in the response window
            spike_mask = ((spike_times >= presentation_time) & 
                         (spike_times < presentation_time + response_window))
            responses[stim_idx] = np.sum(spike_mask)
    
    print(f"Response statistics - Mean: {responses.mean():.2f}, Std: {responses.std():.2f}")
    print(f"Non-zero responses: {np.sum(responses > 0)} / {len(responses)}")
    
    return responses

def custom_kConvNetStyle(stim, frames_list):
    """
    Custom version of kConvNetStyle adapted for different image sizes
    
    Parameters:
    stim: stimulus array of shape (num_samples, height*width)
    frames_list: list of frame indices for temporal dynamics
    
    Returns:
    X: reshaped array for CNN input
    """
    num_samples, img_size = stim.shape
    img_dim = int(np.sqrt(img_size))  # Assuming square images
    
    # Reshape to (samples, height, width)
    stim_reshaped = stim.reshape(num_samples, img_dim, img_dim)
    
    # Add temporal dimension (for now, just replicate frames)
    num_frames = len(frames_list)
    X = np.zeros((num_samples, img_dim, img_dim, num_frames))
    
    for i, frame_idx in enumerate(frames_list):
        # For simplicity, use the same frame (no temporal dynamics in this version)
        X[:, :, :, i] = stim_reshaped
    
    return X

def main():
    """
    Main function to run the custom RF estimation
    """
    # Import configuration
    from config import *

    # Validate configuration
    validate_config()
    print_config()

    # Create results directory
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    print("=== Custom RF Estimation Script ===")
    print("Loading and preprocessing data...")

    # Load natural images
    stim = load_custom_images(IMAGE_FILE)
    movieSize = np.shape(stim)
    imSize = (movieSize[0], movieSize[1])
    movieLength = movieSize[2]

    print(f"Image size: {imSize}, Number of images: {movieLength}")

    # Load neural data
    spike_times, stimulus_indices = load_kilosort_data(KILOSORT_FILE, NEURON_ID)

    # Compute neural responses
    neural_responses = compute_neural_responses(spike_times, stimulus_indices,
                                              movieLength, STIMULUS_DURATION, RESPONSE_WINDOW)
    
    # Prepare data for CNN
    print("Preparing data for CNN...")
    
    # Reshape stimuli to (samples, pixels)
    stim = np.transpose(np.reshape(stim, (imSize[0]*imSize[1], movieLength)))
    
    # Split into train/validation/test sets
    estIdx = slice(0, int(TRAIN_RATIO*movieLength))
    regIdx = slice(int(TRAIN_RATIO*movieLength), int((TRAIN_RATIO+VALIDATION_RATIO)*movieLength))
    predIdx = slice(int((TRAIN_RATIO+VALIDATION_RATIO)*movieLength), movieLength)

    estSet, regSet, predSet = stim[estIdx], stim[regIdx], stim[predIdx]
    y_est, y_reg, y_pred = neural_responses[estIdx], neural_responses[regIdx], neural_responses[predIdx]

    print(f"Training set: {estSet.shape}, Validation set: {regSet.shape}, Test set: {predSet.shape}")

    # Normalize datasets
    if NORMALIZE_IMAGES:
        print("Normalizing datasets...")
        estSet, regSet, predSet = (buildCalcAndScale(dataSet) for dataSet in [estSet, regSet, predSet])

    # Add temporal dynamics
    print("Adding temporal dynamics...")
    estSet, regSet, predSet = (custom_kConvNetStyle(dataSet, FRAME_INDICES) for dataSet in [estSet, regSet, predSet])

    # Model parameters
    Input_Shape = estSet.shape[1:]
    numRows = Input_Shape[0]
    numCols = Input_Shape[1]
    numFrames = Input_Shape[2]

    convImageSize = conv_output_length(numRows, FILTER_SIZE, 'valid', STRIDE)
    
    print(f"Input shape: {Input_Shape}")
    print(f"Conv output size: {convImageSize}")
    
    print("=== Starting Model Training ===")
    
    # Model Pass 1: Filter Estimate Pass
    print("Training Pass 1: Filter estimation...")
    model = model_pass1(Input_Shape, FILTER_SIZE, STRIDE, convImageSize)
    model.summary()

    optimizerFunction = keras.optimizers.Adam(learning_rate=LEARNING_RATE)
    model.compile(loss='mse', optimizer=optimizerFunction)

    earlyStop = keras.callbacks.EarlyStopping(monitor=MONITOR_METRIC, patience=EARLY_STOP_PATIENCE, verbose=0, mode='auto')
    mc = ModelCheckpoint(get_model_filename(1, NEURON_ID), monitor=MONITOR_METRIC, mode='min', verbose=1)

    batch_size = min(BATCH_SIZE, len(estSet)//4) if BATCH_SIZE_AUTO_ADJUST else BATCH_SIZE
    history1 = model.fit(estSet, y_est, validation_data=(regSet, y_reg), epochs=MAX_EPOCHS,
                        batch_size=batch_size, callbacks=[earlyStop, mc], verbose=VERBOSE_TRAINING)
    
    # Get Pass 1 weights
    weights = model.get_weights()
    Initial_Filter_Weights = [weights[0], weights[1]]
    Initial_exp = np.asarray([1])
    
    # Model Pass 2: Power Law Pass
    print("Training Pass 2: Power law refinement...")
    model2 = model_pass2(Input_Shape, FILTER_SIZE, STRIDE, convImageSize,
                        Initial_Filter_Weights, Initial_exp)
    model2.summary()

    optimizerFunction = keras.optimizers.Adam(learning_rate=LEARNING_RATE)
    model2.compile(loss='mse', optimizer=optimizerFunction)

    earlyStop = keras.callbacks.EarlyStopping(monitor=MONITOR_METRIC, patience=EARLY_STOP_PATIENCE, verbose=0, mode='auto')
    mc = ModelCheckpoint(get_model_filename(2, NEURON_ID), monitor=MONITOR_METRIC, mode='min', verbose=1)

    history2 = model2.fit(estSet, y_est, validation_data=(regSet, y_reg), epochs=MAX_EPOCHS,
                         batch_size=batch_size, callbacks=[earlyStop, mc], verbose=VERBOSE_TRAINING)
    
    print("=== Training Complete ===")
    print(f"Results saved in '{RESULTS_DIR}/' directory")
    print("Next: Run visualization and analysis script")

    # Save training histories for later visualization
    import pickle
    with open(os.path.join(RESULTS_DIR, f'training_history_neuron{NEURON_ID}.pkl'), 'wb') as f:
        pickle.dump({'history1': history1.history, 'history2': history2.history}, f)

if __name__ == "__main__":
    main()
